/**
 * 训练指标SSE客户端
 * 提供实时训练指标数据推送功能
 */

class TrainingMetricsSSE {
    constructor(trainingId, options = {}) {
        this.trainingId = trainingId;
        this.eventSource = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
        this.reconnectDelay = options.reconnectDelay || 1000;
        this.baseUrl = options.baseUrl || '/backend';
        
        // 回调函数
        this.onMetricsUpdate = options.onMetricsUpdate || this.defaultOnMetricsUpdate;
        this.onConnected = options.onConnected || this.defaultOnConnected;
        this.onError = options.onError || this.defaultOnError;
        this.onClose = options.onClose || this.defaultOnClose;
        this.onHeartbeat = options.onHeartbeat || this.defaultOnHeartbeat;
        
        // 状态
        this.isConnected = false;
        this.lastUpdateTime = null;
        
        // 降级轮询相关
        this.pollingInterval = null;
        this.pollingDelay = options.pollingDelay || 10000; // 10秒
        
        console.log(`TrainingMetricsSSE initialized for training ${trainingId}`);
    }
    
    /**
     * 开始连接SSE
     */
    connect() {
        if (this.eventSource) {
            this.disconnect();
        }
        
        // 检查浏览器是否支持SSE
        if (!this.supportsSSE()) {
            console.warn('Browser does not support Server-Sent Events, falling back to polling');
            this.fallbackToPolling();
            return;
        }
        
        const url = `${this.baseUrl}/training/${this.trainingId}/metrics-stream`;
        console.log(`Connecting to SSE: ${url}`);
        
        try {
            this.eventSource = new EventSource(url);
            this.setupEventListeners();
        } catch (error) {
            console.error('Failed to create EventSource:', error);
            this.handleConnectionError(error);
        }
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 连接建立
        this.eventSource.addEventListener('connected', (event) => {
            const data = JSON.parse(event.data);
            console.log('SSE Connected:', data);
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.onConnected(data);
        });
        
        // 指标数据更新
        this.eventSource.addEventListener('metrics', (event) => {
            const data = JSON.parse(event.data);
            this.lastUpdateTime = new Date();
            this.onMetricsUpdate(data);
        });
        
        // 最终数据（训练完成）
        this.eventSource.addEventListener('final', (event) => {
            const data = JSON.parse(event.data);
            console.log('Final metrics received:', data);
            this.onMetricsUpdate(data);
        });
        
        // 心跳
        this.eventSource.addEventListener('heartbeat', (event) => {
            const data = JSON.parse(event.data);
            this.onHeartbeat(data);
        });
        
        // 连接关闭
        this.eventSource.addEventListener('close', (event) => {
            const data = JSON.parse(event.data);
            console.log('SSE Connection closed:', data);
            this.isConnected = false;
            this.onClose(data);
            this.disconnect();
        });
        
        // 错误事件
        this.eventSource.addEventListener('error', (event) => {
            const data = JSON.parse(event.data);
            console.error('SSE Error event:', data);
            this.onError(data);
        });
        
        // 连接错误
        this.eventSource.onerror = (event) => {
            console.error('SSE Connection error:', event);
            this.isConnected = false;
            this.handleConnectionError(event);
        };
        
        // 连接打开
        this.eventSource.onopen = (event) => {
            console.log('SSE Connection opened');
        };
    }
    
    /**
     * 处理连接错误
     */
    handleConnectionError(error) {
        this.isConnected = false;
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
            
            console.log(`Reconnecting in ${delay}ms... Attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
            
            setTimeout(() => {
                this.connect();
            }, delay);
        } else {
            console.error('Max reconnection attempts reached, falling back to polling');
            this.fallbackToPolling();
        }
    }
    
    /**
     * 断开SSE连接
     */
    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isConnected = false;
        
        // 清理轮询
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }
    
    /**
     * 降级到轮询模式
     */
    fallbackToPolling() {
        console.log('Falling back to polling mode');
        
        // 清理SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        
        // 开始轮询
        this.pollingInterval = setInterval(() => {
            this.pollMetrics();
        }, this.pollingDelay);
        
        // 立即执行一次
        this.pollMetrics();
    }
    
    /**
     * 轮询获取指标
     */
    async pollMetrics() {
        try {
            const response = await fetch(`${this.baseUrl}/training/${this.trainingId}/metrics`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            this.lastUpdateTime = new Date();
            this.onMetricsUpdate(data);
            
            // 如果训练完成，停止轮询
            if (['completed', 'failed', 'cancelled'].includes(data.status)) {
                console.log('Training finished, stopping polling');
                this.disconnect();
                this.onClose({ message: 'Training finished', status: data.status });
            }
            
        } catch (error) {
            console.error('Polling error:', error);
            this.onError({ error: error.message, timestamp: Date.now() });
        }
    }
    
    /**
     * 检查浏览器是否支持SSE
     */
    supportsSSE() {
        return typeof EventSource !== 'undefined';
    }
    
    /**
     * 获取连接状态
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            lastUpdateTime: this.lastUpdateTime,
            mode: this.eventSource ? 'sse' : 'polling'
        };
    }
    
    // 默认回调函数
    defaultOnMetricsUpdate(data) {
        console.log('Metrics updated:', data);
    }
    
    defaultOnConnected(data) {
        console.log('Connected to metrics stream:', data);
    }
    
    defaultOnError(error) {
        console.error('Metrics stream error:', error);
    }
    
    defaultOnClose(data) {
        console.log('Metrics stream closed:', data);
    }
    
    defaultOnHeartbeat(data) {
        console.log('Heartbeat:', data.timestamp);
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TrainingMetricsSSE;
} else if (typeof window !== 'undefined') {
    window.TrainingMetricsSSE = TrainingMetricsSSE;
}
