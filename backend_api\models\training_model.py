"""
训练模型管理相关的数据模型
"""

from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid
from backend_api.models.training import TrainingTask


class TrainingModel(models.Model):
    """
    训练模型信息模型
    
    用于存储训练过程中生成的模型信息，包括模型路径、精度、召回率等
    """
    # 关联的训练任务
    task = models.ForeignKey(TrainingTask, on_delete=models.CASCADE, related_name='models', null=True, blank=True)
    
    # 模型基本信息
    model_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="模型名称")
    model_path = models.CharField(max_length=1024, null=True, blank=True, verbose_name="模型路径")
    
    # 模型性能指标
    accuracy = models.FloatField(default=0.0, verbose_name="准确率")
    precision = models.FloatField(default=0.0, verbose_name="精度")
    recall = models.FloatField(default=0.0, verbose_name="召回率")
    inference_speed = models.FloatField(default=0.0, verbose_name="推理速度(FPS)")
    model_size_mb = models.FloatField(default=0.0, verbose_name="模型大小(MB)")
    
    # 添加缺失的字段
    inference_time_ms = models.FloatField(default=0.0, verbose_name="推理时间(毫秒)")
    num_classes = models.IntegerField(default=0, verbose_name="类别数量")
    architecture = models.CharField(max_length=100, blank=True, null=True, verbose_name="模型架构")
    fitness = models.FloatField(default=0.0, verbose_name="适应度")
    notes = models.TextField(blank=True, null=True, verbose_name="备注")
    
    # 模型训练信息
    is_best = models.BooleanField(default=False, verbose_name="是否为最佳模型")
    epoch = models.IntegerField(default=0, verbose_name="训练轮次")
    
    # 模型转换信息
    is_converted = models.BooleanField(default=False, verbose_name="模型格式转换")
    conversion_status = models.CharField(max_length=20, default='pending', blank=True, null=True, verbose_name="转换状态")
    converted_model_path = models.CharField(max_length=1024, blank=True, null=True, verbose_name="转换后的模型路径")
    
    # 导出信息
    export_status = models.CharField(
        max_length=20, 
        choices=[
            ('pending', '待导出'),
            ('exporting', '导出中'),
            ('completed', '已完成'),
            ('failed', '导出失败')
        ],
        default='pending',
        verbose_name="导出状态"
    )
    
    # 服务器信息（用于远程访问模型文件）
    server_ip = models.CharField(max_length=255, blank=True, null=True, verbose_name="服务器IP")
    server_port = models.CharField(max_length=10, blank=True, null=True, verbose_name="服务器端口")
    server_password = models.CharField(max_length=255, blank=True, null=True, verbose_name="服务器密码")
    
    # 用户信息
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True, blank=True, verbose_name="创建者")
    
    # 时间戳
    created_time = models.DateTimeField(default=timezone.now, blank=True, null=True, verbose_name="创建时间")
    updated_time = models.DateTimeField(default=timezone.now, blank=True, null=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "训练模型"
        verbose_name_plural = "训练模型"
        ordering = ['-created_time']
        
    def __str__(self):
        return f"{self.model_name} - 准确率: {self.accuracy:.4f}"
    
    @property
    def is_exported(self):
        """检查模型格式转换状态"""
        return self.is_converted
    
    @property
    def performance_summary(self):
        """获取性能摘要"""
        return {
            'accuracy': self.accuracy,
            'precision': self.precision,
            'recall': self.recall,
            'inference_speed': self.inference_speed,
            'model_size_mb': self.model_size_mb
        }
    
    def update_metrics(self, metrics_data):
        """更新模型指标"""
        for key, value in metrics_data.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_time = timezone.now()
        self.save()



